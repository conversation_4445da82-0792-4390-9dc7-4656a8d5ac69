defaults:
  - model: transformer      # loads config/model/transformer.yaml

hydra:
  run:
    dir: ./my_experiments/${now:%Y-%m-%d_%H-%M-%S}

paths:
  dict_path: data/unique_sorted_lines_dictionary.txt
  train_path: data/random_cont_test_8.0_train.txt
  test_path: data/random_cont_test_8.0_test.txt
  save_dir: log

training:
  seed: 42
  val_ratio: 0.2
  batch_size: 4096
  num_epochs: 20
  learning_rate: 0.001
  loss_multiplier: 1.0
  partial_threshold: 1.0  # Threshold for element-wise partial matching
  partial_temperature: 10.0  # Temperature for sigmoid soft thresholding
