#!/usr/bin/env python3
"""
Test script to demonstrate the element-wise partial loss function
"""

import torch
import numpy as np

def element_wise_partial_loss(pred_vecs, true_vecs, threshold=0.5, temperature=10.0):
    """
    Differentiable element-wise partial match loss using sigmoid soft thresholding.
    """
    element_errors = torch.abs(pred_vecs - true_vecs)

    # Soft thresholding using sigmoid: outputs close to 1 when error < threshold
    soft_matches = torch.sigmoid(-temperature * (element_errors - threshold))

    match_ratio = torch.mean(soft_matches, dim=-1)  # Average soft match per sample
    return torch.mean(1 - match_ratio)  # Convert to loss (lower is better)

def test_partial_loss():
    print("Testing Element-wise Partial Loss Function")
    print("=" * 50)
    
    # Create some test cases
    batch_size = 3
    vector_dim = 4
    threshold = 0.5
    
    # Ground truth vectors
    true_vecs = torch.tensor([
        [1.0, 2.0, 3.0, 4.0],  # Sample 1
        [0.5, 1.5, 2.5, 3.5],  # Sample 2  
        [2.0, 4.0, 6.0, 8.0],  # Sample 3
    ])
    
    print(f"Ground truth vectors:\n{true_vecs}")
    print(f"Threshold: {threshold}")
    print()
    
    # Test Case 1: Perfect match
    pred_vecs_perfect = true_vecs.clone()
    loss_perfect = element_wise_partial_loss(pred_vecs_perfect, true_vecs, threshold)
    print(f"Test 1 - Perfect Match:")
    print(f"Predicted: {pred_vecs_perfect}")
    print(f"Loss: {loss_perfect:.4f} (should be 0.0)")
    print()
    
    # Test Case 2: Partial match (some elements close)
    pred_vecs_partial = torch.tensor([
        [1.2, 2.3, 3.1, 4.4],  # Sample 1: 3/4 elements within threshold
        [0.8, 1.2, 2.8, 3.2],  # Sample 2: 4/4 elements within threshold  
        [2.4, 4.6, 5.5, 8.3],  # Sample 3: 2/4 elements within threshold
    ])
    loss_partial = element_wise_partial_loss(pred_vecs_partial, true_vecs, threshold)
    
    # Calculate expected match ratios manually
    errors = torch.abs(pred_vecs_partial - true_vecs)
    matches = (errors < threshold).float()
    match_ratios = torch.mean(matches, dim=-1)
    
    print(f"Test 2 - Partial Match:")
    print(f"Predicted: {pred_vecs_partial}")
    print(f"Element errors:\n{errors}")
    print(f"Elements within threshold:\n{matches}")
    print(f"Match ratios per sample: {match_ratios}")
    print(f"Average match ratio: {torch.mean(match_ratios):.4f}")
    print(f"Loss: {loss_partial:.4f} (should be {1 - torch.mean(match_ratios):.4f})")
    print()
    
    # Test Case 3: No match (all elements far)
    pred_vecs_no_match = torch.tensor([
        [5.0, 6.0, 7.0, 8.0],  # Sample 1: all elements > threshold away
        [5.0, 6.0, 7.0, 8.0],  # Sample 2: all elements > threshold away
        [10.0, 12.0, 14.0, 16.0],  # Sample 3: all elements > threshold away
    ])
    loss_no_match = element_wise_partial_loss(pred_vecs_no_match, true_vecs, threshold)
    print(f"Test 3 - No Match:")
    print(f"Predicted: {pred_vecs_no_match}")
    print(f"Loss: {loss_no_match:.4f} (should be 1.0)")
    print()
    
    # Test Case 4: Different thresholds
    print("Test 4 - Effect of Different Thresholds:")
    test_pred = torch.tensor([[1.3, 2.2, 3.4, 4.1]])
    test_true = torch.tensor([[1.0, 2.0, 3.0, 4.0]])
    
    for thresh in [0.1, 0.2, 0.3, 0.4, 0.5]:
        loss = element_wise_partial_loss(test_pred, test_true, thresh)
        print(f"  Threshold {thresh}: Loss: {loss:.4f}")

def test_differentiability():
    print("\n" + "=" * 50)
    print("Testing Differentiability")
    print("=" * 50)

    # Create tensors that require gradients
    pred_vecs = torch.tensor([[1.3, 2.2, 3.4, 4.1]], requires_grad=True)
    true_vecs = torch.tensor([[1.0, 2.0, 3.0, 4.0]])

    # Compute loss
    loss = element_wise_partial_loss(pred_vecs, true_vecs, threshold=0.5, temperature=10.0)

    print(f"Predicted: {pred_vecs}")
    print(f"True: {true_vecs}")
    print(f"Loss: {loss:.4f}")

    # Compute gradients
    loss.backward()

    print(f"Gradients: {pred_vecs.grad}")
    print(f"Gradient norm: {pred_vecs.grad.norm():.4f}")

    if pred_vecs.grad.norm() > 1e-6:
        print("✅ Loss is differentiable - gradients computed successfully!")
    else:
        print("❌ Loss may not be differentiable - gradients are near zero!")

    # Test different temperatures
    print(f"\nEffect of Temperature on Gradients:")
    for temp in [1.0, 5.0, 10.0, 20.0]:
        pred_test = torch.tensor([[1.3, 2.2, 3.4, 4.1]], requires_grad=True)
        loss_test = element_wise_partial_loss(pred_test, true_vecs, threshold=0.5, temperature=temp)
        loss_test.backward()
        print(f"  Temperature {temp:4.1f}: Loss={loss_test:.4f}, Grad norm={pred_test.grad.norm():.4f}")

if __name__ == "__main__":
    test_partial_loss()
    test_differentiability()
