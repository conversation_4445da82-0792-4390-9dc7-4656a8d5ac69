import torch
from torch.utils.data import Dataset
import ast
import logging
log = logging.getLogger(__name__)


class DictionaryTokenizer:
    def __init__(self, dict_path):
        import ast
        self.seq_to_idx = {}
        self.idx_to_seq = {}
        vectors = []

        with open(dict_path, 'r') as f:
            for idx, line in enumerate(f):
                seq_str = line.strip()
                self.seq_to_idx[seq_str] = idx
                self.idx_to_seq[idx] = seq_str

                try:
                    vec = torch.tensor(ast.literal_eval(seq_str), dtype=torch.float32)
                except Exception as e:
                    log.warning(f"Failed to parse vector: {seq_str}, error: {e}")
                    vec = torch.zeros(1)
                vectors.append(vec)

        max_dim = max(v.numel() for v in vectors)
        padded = [torch.cat([v, torch.zeros(max_dim - v.numel())]) for v in vectors]
        self.class_values = torch.stack(padded)  # shape: (num_classes, vector_dim)

        self.vocab_size = len(self.seq_to_idx)
        log.info(f"Dictionary size: {self.vocab_size}, vector dim: {self.class_values.shape[1]}")

    def encode(self, sequence):
        seq_str = str(sequence)
        return self.seq_to_idx.get(seq_str, None)

    def decode(self, idx):
        return self.idx_to_seq.get(idx, None)

    def get_vector(self, idx_tensor):
        return self.class_values[idx_tensor]

    
class LinewiseSequenceDataset(Dataset):
    def __init__(self, file_path, tokenizer):
        self.samples = self._load_samples(file_path)
        self.tokenizer = tokenizer
        self.data = self._tokenize_samples(self.samples)

    def _load_samples(self, file_path):
        samples = []
        with open(file_path, 'r') as f:
            for line in f:
                parts = line.strip().split('] [')
                if len(parts) == 2:
                    try:
                        input_lines = ast.literal_eval(parts[0] + ']')
                        target_line = ast.literal_eval('[' + parts[1])
                        samples.append((input_lines, target_line))
                    except:
                        continue
        return samples

    def _tokenize_samples(self, samples):
        X, y = [], []
        for input_lines, target_line in samples:
            input_ids = [self.tokenizer.encode(seq) for seq in input_lines]
            target_id = self.tokenizer.encode(target_line)
            if None not in input_ids and target_id is not None:
                X.append(input_ids)
                y.append(target_id)
        return list(zip(X, y))

    def __len__(self):
        # return min(10000, len(self.data))
        return len(self.data)

    def __getitem__(self, idx):
        x, y = self.data[idx]
        return torch.tensor(x, dtype=torch.long), torch.tensor(y, dtype=torch.long)
