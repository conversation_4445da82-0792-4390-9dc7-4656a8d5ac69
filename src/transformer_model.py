import torch
import torch.nn as nn

class TransformerDictionarySequencePredictor(nn.Module):
    def __init__(self, vocab_size, d_model=128, nhead=4, num_layers=3,
                 dim_feedforward=512, dropout=0.1, seq_len=5):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.positional_encoding = nn.Parameter(torch.zeros(1, seq_len, d_model))
        encoder_layer = nn.TransformerEncoderLayer(
            d_model, nhead, dim_feedforward, dropout, batch_first=True
        )
        self.encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        self.output_layer = nn.Linear(d_model, vocab_size)

    def forward(self, x):
        x = self.embedding(x) + self.positional_encoding[:, :x.size(1), :]
        x = self.encoder(x)
        x = self.output_layer(x[:, -1, :])  # Use last token
        return x
