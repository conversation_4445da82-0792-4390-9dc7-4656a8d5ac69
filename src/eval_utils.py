import os
import torch
import matplotlib.pyplot as plt
import numpy as np
from collections import Counter
from sklearn.metrics import confusion_matrix
import seaborn as sns
import logging
log = logging.getLogger(__name__)

def visualize_training_progress(train_losses, train_accuracies, save_dir,
                                 val_losses=None, val_rmses=None):
    os.makedirs(save_dir, exist_ok=True)
    epochs = list(range(1, len(train_losses) + 1))
    plt.figure(figsize=(12, 5))

    # Loss Plot
    plt.subplot(1, 2, 1)
    plt.plot(epochs, train_losses, label='Train Loss')
    if val_losses:
        plt.plot(epochs, val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Loss Over Epochs')
    plt.legend()
    plt.grid(True)

    # RMSE Plot
    plt.subplot(1, 2, 2)
    plt.plot(epochs, train_accuracies, label='Train RMSE', color='orange')
    if val_rmses:
        plt.plot(epochs, val_rmses, label='Val RMSE', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('RMSE')
    plt.title('RMSE Over Epochs')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_validation_loss_accuracy.png'))
    plt.close()

def visualize_predictions(X, y, model, tokenizer, save_dir, num_samples=5):
    os.makedirs(save_dir, exist_ok=True)
    results = []
    model.eval()
    device = next(model.parameters()).device

    with torch.no_grad():
        for i in range(min(num_samples, len(X))):
            x = X[i].unsqueeze(0).to(device)
            logits = model(x)
            pred = logits.argmax(dim=-1).item()
            input_strs = [tokenizer.decode(idx.item()) for idx in X[i]]
            target_str = tokenizer.decode(y[i].item())
            pred_str = tokenizer.decode(pred)
            results.append((input_strs, target_str, pred_str))

    with open(os.path.join(save_dir, 'prediction_samples.txt'), 'w') as f:
        for i, (inp, tgt, pred) in enumerate(results):
            f.write(f"Sample {i+1}:\n")
            f.write(f"Input: {inp}\nTarget: {tgt}\nPredicted: {pred}\n\n")

    plt.figure(figsize=(10, 2 * num_samples))
    for i, (inp, tgt, pred) in enumerate(results):
        plt.subplot(num_samples, 1, i + 1)
        plt.barh([0], [1], color='green' if tgt == pred else 'red', alpha=0.6)
        plt.title(f"Sample {i+1} | {'Correct' if tgt == pred else 'Incorrect'}\nTarget: {tgt} | Predicted: {pred}")
        plt.yticks([])
        plt.xticks([])
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'prediction_results.png'))
    plt.close()

def plot_confusion_matrix(y_true, y_pred, tokenizer, save_dir, top_n=20):
    os.makedirs(save_dir, exist_ok=True)

    label_accuracy = {}
    for label in set(y_true):
        indices = [i for i, t in enumerate(y_true) if t == label]
        if indices:
            correct = sum(1 for i in indices if y_pred[i] == label)
            acc = correct / len(indices)
            label_accuracy[label] = acc

    perfect = [label for label, acc in label_accuracy.items() if acc == 1.0]
    log.info(f"Labels with 100% accuracy: {len(perfect)}")
    log.info("Perfect labels: %s", perfect[:10])

    with open(os.path.join(save_dir, f'sorted_by_accuracy_top{top_n}.txt'), 'w') as f:
        for label, acc in sorted(label_accuracy.items(), key=lambda x: x[1], reverse=True):
            f.write(f"{tokenizer.decode(label)}: {acc:.3f}\n")

    top_labels = [l for l, _ in sorted(label_accuracy.items(), key=lambda x: x[1], reverse=True)[:top_n]]
    idx_map = {l: i for i, l in enumerate(top_labels)}

    filtered = [(idx_map[t], idx_map[p]) for t, p in zip(y_true, y_pred) if t in idx_map and p in idx_map]
    if not filtered:
        log.warning("No predictions available for top-N confusion matrix.")
        return

    y_true_f, y_pred_f = zip(*filtered)
    cm = confusion_matrix(y_true_f, y_pred_f, labels=list(range(top_n)))
    cm = cm.astype(np.float32)
    row_sums = cm.sum(axis=1, keepdims=True)
    cm = np.divide(cm, row_sums, where=row_sums != 0)

    label_names = [f"{tokenizer.decode(l)}\n(acc: {label_accuracy[l]:.2f})" for l in top_labels]
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt=".2f", cmap="Blues",
                xticklabels=label_names, yticklabels=label_names,
                vmin=0, vmax=1)
    plt.xlabel("Predicted")
    plt.ylabel("True")
    plt.title(f"Confusion Matrix (Top {top_n} Accurate Labels)")
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'confusion_matrix_top{top_n}.png'))
    plt.close()

def plot_error_histogram(y_true, y_pred, tokenizer, save_dir, top_n=20):
    os.makedirs(save_dir, exist_ok=True)
    errors = [t for t, p in zip(y_true, y_pred) if t != p]
    error_counts = Counter(errors).most_common(top_n)

    labels = [tokenizer.decode(idx) for idx, _ in error_counts]
    counts = [c for _, c in error_counts]

    plt.figure(figsize=(12, 6))
    plt.barh(labels, counts, color='red', alpha=0.7)
    plt.xlabel("Error Count")
    plt.title(f"Most Frequent Errors (Top {top_n} True Labels)")
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'error_histogram_top{top_n}.png'))
    plt.close()

def evaluate_model(model, data_loader, tokenizer, save_dir, device, mode="", use_checkpoint=True):
    if use_checkpoint:
        checkpoint_path = os.path.join(save_dir, 'transformer_dictionary_model.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location=device))
        model.to(device)
    model.eval()

    y_true = []
    y_pred = []
    with torch.no_grad():
        for x, y in data_loader:
            x, y = x.to(device), y.to(device)
            logits = model(x)
            preds = logits.argmax(dim=-1)
            y_true.extend(y.cpu().tolist())
            y_pred.extend(preds.cpu().tolist())

    y_true_tensor = torch.tensor(y_true, dtype=torch.long, device=device)
    y_pred_tensor = torch.tensor(y_pred, dtype=torch.long, device=device)

    y_true_vecs = tokenizer.get_vector(y_true_tensor).float()
    y_pred_vecs = tokenizer.get_vector(y_pred_tensor).float()

    rmse = torch.sqrt(torch.nn.functional.mse_loss(y_pred_vecs, y_true_vecs)).item()
    log.info(f"Final {mode} RMSE: {rmse:.4f}")

    plot_confusion_matrix(y_true, y_pred, tokenizer, save_dir)
    plot_error_histogram(y_true, y_pred, tokenizer, save_dir)
    return rmse
