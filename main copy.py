import torch
import numpy as np
import random
import logging
from pathlib import Path

from torch.utils.data import DataLoader, random_split
from omegaconf import DictConfig, OmegaConf
from hydra.core.hydra_config import HydraConfig
import hydra
import os

from src.dataset import LinewiseSequenceDataset, DictionaryTokenizer
from src.eval_utils import evaluate_model, visualize_training_progress

# Set up logger (Hydra configures the root logger automatically)
log = logging.getLogger(__name__)


def set_seed(seed: int):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


@hydra.main(config_path="config", config_name="config", version_base="1.3")
def main(cfg: DictConfig):
    log.info("Configuration:\n%s", OmegaConf.to_yaml(cfg))
    set_seed(cfg.training.seed)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    log.info(f"Using device: {device}")

    # Ensure save_dir is inside Hydra's output dir

    save_dir = Path(HydraConfig.get().runtime.output_dir) / cfg.paths.save_dir
    save_dir.mkdir(parents=True, exist_ok=True)
    log.info(f"Saving outputs to: {save_dir}")

    tokenizer = DictionaryTokenizer(cfg.paths.dict_path)

    full_train_dataset = LinewiseSequenceDataset(cfg.paths.train_path, tokenizer)
    test_dataset = LinewiseSequenceDataset(cfg.paths.test_path, tokenizer)

    val_size = int(len(full_train_dataset) * cfg.training.val_ratio)
    train_size = len(full_train_dataset) - val_size
    train_dataset, val_dataset = random_split(full_train_dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=cfg.training.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=cfg.training.batch_size)
    test_loader = DataLoader(test_dataset, batch_size=cfg.training.batch_size)

    model = hydra.utils.instantiate(cfg.model, vocab_size=tokenizer.vocab_size).to(device)
    log.info(f"Model has {count_parameters(model):,} trainable parameters.")

    optimizer = torch.optim.Adam(model.parameters(), lr=cfg.training.learning_rate)
    criterion = torch.nn.CrossEntropyLoss()

    train_losses, train_accuracies = [], []
    val_losses, val_accuracies = [], []
    best_model_path = save_dir / "transformer_dictionary_model.pth"
    best_val_loss = float("inf")


    mse_loss = torch.nn.MSELoss()

    tokenizer.class_values = tokenizer.class_values.to(device)

    for epoch in range(cfg.training.num_epochs):
        model.train()
        total_loss = total_correct = total_count = 0

        for x_batch, y_batch in train_loader:
            x_batch, y_batch = x_batch.to(device), y_batch.to(device)
            optimizer.zero_grad()

            logits = model(x_batch)  # shape: (batch_size, num_classes)
            probs = torch.softmax(logits, dim=-1)  # shape: (batch_size, num_classes)

            # Soft lookup of prediction vectors (preserve gradient)
            pred_vecs = probs @ tokenizer.class_values  # (batch_size, vector_dim)

            # Hard labels for MSE target (still differentiable)
            true_vecs = tokenizer.get_vector(y_batch)  # (batch_size, vector_dim)

            loss = cfg.training.loss_multiplier * mse_loss(pred_vecs, true_vecs)
            loss.backward()
            optimizer.step()

            total_loss += loss.item() * x_batch.size(0)

            # Track accuracy using argmax (not used in loss)
            pred_labels = logits.argmax(dim=-1)
            total_correct += (pred_labels == y_batch).sum().item()
            total_count += x_batch.size(0)

        train_losses.append(total_loss / total_count)
        train_accuracies.append(total_correct / total_count)

        # Validation
        model.eval()
        val_loss = val_correct = val_count = 0

        with torch.no_grad():
            for x_batch, y_batch in val_loader:
                x_batch, y_batch = x_batch.to(device), y_batch.to(device)
                logits = model(x_batch)
                probs = torch.softmax(logits, dim=-1)

                # Soft lookup of prediction vectors
                pred_vecs = probs @ tokenizer.class_values  # (batch_size, vector_dim)
                true_vecs = tokenizer.get_vector(y_batch)   # (batch_size, vector_dim)

                loss = cfg.training.loss_multiplier * mse_loss(pred_vecs, true_vecs)
                val_loss += loss.item() * x_batch.size(0)

                # Accuracy using argmax (non-differentiable, fine for logging)
                pred_labels = logits.argmax(dim=-1)
                val_correct += (pred_labels == y_batch).sum().item()
                val_count += x_batch.size(0)

        val_losses.append(val_loss / val_count)
        val_accuracies.append(val_correct / val_count)

        # Save best model
        if val_losses[-1] < best_val_loss:
            best_val_loss = val_losses[-1]
            torch.save(model.state_dict(), best_model_path)
            log.info(f"Epoch {epoch + 1} is best so far!")

        # Optional logging every 10 epochs
        if (epoch + 1) % 10 == 0 or epoch == 0:
            log.info(f"Epoch {epoch + 1}: "
                    f"Train Loss={train_losses[-1]:.4f}, Acc={train_accuracies[-1]:.4f} | "
                    f"Val Loss={val_losses[-1]:.4f}, Acc={val_accuracies[-1]:.4f}")

    log.info(f"Model saved at best epoch with val loss: {best_val_loss:.4f}")

    visualize_training_progress(train_losses, train_accuracies, save_dir,
                                val_losses=val_losses, val_accuracies=val_accuracies)

    train_acc = evaluate_model(model, train_loader, tokenizer, save_dir, device, mode="training")
    val_acc = evaluate_model(model, val_loader, tokenizer, save_dir, device, mode="validation")
    test_acc = evaluate_model(model, test_loader, tokenizer, save_dir, device, mode="testing")


    import csv
    

    # Access sweep directory (only in multirun mode)
    # sweep_dir = Path(HydraConfig.get().sweep.dir) if HydraConfig.initialized() and HydraConfig.get().mode == "MULTIRUN" else save_dir
    metrics_path = Path(os.getcwd()) / "all_metrics.csv"
    log.info(f"Save to: {metrics_path}")

    # Append to CSV (create header if not exists)
    fieldnames = ["d_model", "nhead", "num_layers", "dropout", "dim_feedforward", "num_parameters", 
                "train_acc", "val_acc", "test_acc"]


    with open(metrics_path, "a", newline="") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writerow({
            "d_model": cfg.model.d_model,
            "nhead": cfg.model.nhead,
            "num_layers": cfg.model.num_layers,
            "dropout": cfg.model.dropout,
            "dim_feedforward": cfg.model.dim_feedforward,
            "num_parameters": count_parameters(model),
            "train_acc": f"{train_acc:.4f}",
            "val_acc": f"{val_acc:.4f}",
            "test_acc": f"{test_acc:.4f}",
        })



if __name__ == "__main__":
    main()


# python main.py model.<key>=<value>
# lr = 0.001, num_epochs = 50
# python main.py -m model.d_model=64 model.nhead=2,4,8 model.num_layers=2,3 model.dropout=0.1,0.3 model.dim_feedforward=256
# python main.py -m model.d_model=128 model.nhead=2,4,8 model.num_layers=2,3 model.dropout=0.1,0.3 model.dim_feedforward=512
# python main.py -m model.d_model=256 model.nhead=2,4,8 model.num_layers=2,3 model.dropout=0.1,0.3 model.dim_feedforward=1024


# lr = 0.0001, num_epochs = 200
# python main.py -m model.d_model=8 model.nhead=1,3,5 model.num_layers=1,3 model.dropout=0.1,0.3 model.dim_feedforward=32
# python main.py -m model.d_model=16 model.nhead=1,3,5 model.num_layers=1,3 model.dropout=0.1,0.3 model.dim_feedforward=64
# python main.py -m model.d_model=32 model.nhead=1,3,5 model.num_layers=1,3 model.dropout=0.1,0.3 model.dim_feedforward=128
# python main.py -m model.d_model=64 model.nhead=1,3,5 model.num_layers=1,3 model.dropout=0.1,0.3 model.dim_feedforward=256


