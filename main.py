import torch
import numpy as np
import random
import logging
from pathlib import Path

from torch.utils.data import Data<PERSON><PERSON><PERSON>, random_split
from omegaconf import DictConfig, OmegaConf
from hydra.core.hydra_config import HydraConfig
import hydra
import os

from src.dataset import LinewiseSequenceDataset, DictionaryTokenizer
from src.eval_utils import (
    visualize_training_progress,
    plot_confusion_matrix,
    plot_error_histogram,
    evaluate_model,
)

# Set up logger
log = logging.getLogger(__name__)

def set_seed(seed: int):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def element_wise_partial_loss(pred_vecs, true_vecs, threshold=0.5, temperature=10.0):
    """
    Differentiable element-wise partial match loss using sigmoid soft thresholding.

    Mathematical formulation:
    L_partial = (1/N) Σ_{i=1}^N [1 - (1/D) Σ_{j=1}^D sigmoid(-τ(|ŷ_{i,j} - T_{y_i,j}| - σ))]

    Args:
        pred_vecs: ŷ_i ∈ ℝ^{N×D} - predicted token vectors (soft lookup: p_i @ T)
        true_vecs: T_{y_i} ∈ ℝ^{N×D} - ground truth token vectors (hard lookup)
        threshold: σ ∈ ℝ - tolerance for element matching
        temperature: τ ∈ ℝ - sigmoid steepness parameter

    Returns:
        L_partial ∈ ℝ - scalar loss value (0 = perfect match, 1 = no elements match)
    """
    # Compute element-wise absolute errors: |ŷ_{i,j} - T_{y_i,j}|
    element_errors = torch.abs(pred_vecs - true_vecs)  # Shape: (N, D)

    # Soft thresholding: sigmoid(-τ(error - σ))
    # When error < σ: sigmoid output ≈ 1 (good match)
    # When error > σ: sigmoid output ≈ 0 (poor match)
    soft_matches = torch.sigmoid(-temperature * (element_errors - threshold))  # Shape: (N, D)

    # Average soft matches per sample: (1/D) Σ_{j=1}^D sigmoid(...)
    match_ratio_per_sample = torch.mean(soft_matches, dim=-1)  # Shape: (N,)

    # Convert to loss and average over batch: (1/N) Σ_{i=1}^N [1 - match_ratio_i]
    loss_per_sample = 1 - match_ratio_per_sample  # Shape: (N,)
    return torch.mean(loss_per_sample)  # Scalar

def compute_accuracy_metrics(pred_vecs, true_vecs, logits, y_batch, threshold=0.5):
    """
    Compute various accuracy metrics for vector predictions.

    Args:
        pred_vecs: Predicted vectors (batch_size, vector_dim)
        true_vecs: Ground truth vectors (batch_size, vector_dim)
        logits: Raw model outputs (batch_size, vocab_size)
        y_batch: Ground truth token indices (batch_size,)
        threshold: Threshold for element-wise matching

    Returns:
        Dictionary of accuracy metrics
    """
    # 1. Exact Token Match Accuracy (traditional classification accuracy)
    pred_tokens = logits.argmax(dim=-1)
    exact_match_acc = (pred_tokens == y_batch).float().mean().item()

    # 2. Element-wise Partial Match Accuracy (% of elements within threshold)
    element_errors = torch.abs(pred_vecs - true_vecs)
    element_matches = (element_errors < threshold).float()
    partial_match_acc = element_matches.mean().item()

    # 3. Vector-level Accuracy (% of vectors where ALL elements are within threshold)
    vector_matches = (element_matches.mean(dim=-1) == 1.0).float()
    vector_acc = vector_matches.mean().item()

    # 4. Cosine Similarity Accuracy (average cosine similarity)
    cos_sim = torch.nn.functional.cosine_similarity(pred_vecs, true_vecs, dim=-1)
    cos_sim_acc = cos_sim.mean().item()

    # 5. Top-K Token Accuracy (is ground truth in top-k predictions?)
    top5_preds = logits.topk(5, dim=-1)[1]  # Get top-5 token indices
    top5_acc = (top5_preds == y_batch.unsqueeze(-1)).any(dim=-1).float().mean().item()

    return {
        'exact_match': exact_match_acc,
        'partial_match': partial_match_acc,
        'vector_match': vector_acc,
        'cosine_similarity': cos_sim_acc,
        'top5_match': top5_acc
    }

@hydra.main(config_path="config", config_name="config", version_base="1.3")
def main(cfg: DictConfig): 
    log.info("Configuration:\n%s", OmegaConf.to_yaml(cfg))
    set_seed(cfg.training.seed)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    log.info(f"Using device: {device}")

    save_dir = Path(HydraConfig.get().runtime.output_dir) / cfg.paths.save_dir
    save_dir.mkdir(parents=True, exist_ok=True)
    log.info(f"Saving outputs to: {save_dir}")

    tokenizer = DictionaryTokenizer(cfg.paths.dict_path)
    full_train_dataset = LinewiseSequenceDataset(cfg.paths.train_path, tokenizer)
    test_dataset = LinewiseSequenceDataset(cfg.paths.test_path, tokenizer)

    val_size = int(len(full_train_dataset) * cfg.training.val_ratio)
    train_size = len(full_train_dataset) - val_size
    train_dataset, val_dataset = random_split(full_train_dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=cfg.training.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=cfg.training.batch_size)
    test_loader = DataLoader(test_dataset, batch_size=cfg.training.batch_size)

    model = hydra.utils.instantiate(cfg.model, vocab_size=tokenizer.vocab_size).to(device)
    log.info(f"Model has {count_parameters(model):,} trainable parameters.")

    optimizer = torch.optim.Adam(model.parameters(), lr=cfg.training.learning_rate)
    mse_loss = torch.nn.MSELoss()
    tokenizer.class_values = tokenizer.class_values.to(device).float()

    # Get partial loss parameters from config (with defaults)
    partial_threshold = getattr(cfg.training, 'partial_threshold', 0.5)
    partial_temperature = getattr(cfg.training, 'partial_temperature', 10.0)
    log.info(f"Using partial loss threshold: {partial_threshold}, temperature: {partial_temperature}")

    train_losses, train_rmses = [], []
    val_losses, val_rmses = [], []
    val_accuracies = []  # Store validation accuracy metrics
    best_model_path = save_dir / "transformer_dictionary_model.pth"
    best_val_loss = float("inf")

    for epoch in range(cfg.training.num_epochs):
        model.train()
        total_loss = 0
        
        for x_batch, y_batch in train_loader:
            x_batch, y_batch = x_batch.to(device), y_batch.to(device)
            optimizer.zero_grad()
            logits = model(x_batch)
            probs = torch.softmax(logits, dim=-1)
            pred_vecs = probs @ tokenizer.class_values
            true_vecs = tokenizer.get_vector(y_batch)

            # Use element-wise partial loss instead of MSE
            partial_loss = element_wise_partial_loss(pred_vecs, true_vecs,
                                                   threshold=partial_threshold,
                                                   temperature=partial_temperature)
            loss = cfg.training.loss_multiplier * partial_loss
            loss.backward()
            optimizer.step()
            total_loss += loss.item() * x_batch.size(0)

        train_losses.append(total_loss / len(train_loader.dataset))

        # Compute RMSE for train
        model.eval()
        all_preds, all_trues = [], []
        with torch.no_grad():
            for x_batch, y_batch in train_loader:
                x_batch, y_batch = x_batch.to(device), y_batch.to(device)
                logits = model(x_batch)
                probs = torch.softmax(logits, dim=-1)
                pred_vecs = probs @ tokenizer.class_values
                true_vecs = tokenizer.get_vector(y_batch)
                all_preds.append(pred_vecs)
                all_trues.append(true_vecs)
        rmse_train = torch.sqrt(mse_loss(torch.cat(all_preds), torch.cat(all_trues))).item()
        train_rmses.append(rmse_train)

        # Validation
        val_loss = 0
        all_preds, all_trues, all_logits, all_y = [], [], [], []
        with torch.no_grad():
            for x_batch, y_batch in val_loader:
                x_batch, y_batch = x_batch.to(device), y_batch.to(device)
                logits = model(x_batch)
                probs = torch.softmax(logits, dim=-1)
                pred_vecs = probs @ tokenizer.class_values
                true_vecs = tokenizer.get_vector(y_batch)

                # Use element-wise partial loss for validation too
                partial_loss = element_wise_partial_loss(pred_vecs, true_vecs,
                                                       threshold=partial_threshold,
                                                       temperature=partial_temperature)
                loss = cfg.training.loss_multiplier * partial_loss
                val_loss += loss.item() * x_batch.size(0)

                # Collect data for metrics
                all_preds.append(pred_vecs)
                all_trues.append(true_vecs)
                all_logits.append(logits)
                all_y.append(y_batch)

        val_losses.append(val_loss / len(val_loader.dataset))
        rmse_val = torch.sqrt(mse_loss(torch.cat(all_preds), torch.cat(all_trues))).item()
        val_rmses.append(rmse_val)

        # Compute accuracy metrics
        val_metrics = compute_accuracy_metrics(
            torch.cat(all_preds), torch.cat(all_trues),
            torch.cat(all_logits), torch.cat(all_y),
            threshold=partial_threshold
        )
        val_accuracies.append(val_metrics)

        if val_losses[-1] < best_val_loss:
            best_val_loss = val_losses[-1]
            torch.save(model.state_dict(), best_model_path)
            log.info(f"Epoch {epoch + 1} is best so far!")

            # Log comprehensive metrics
            metrics = val_accuracies[-1]
            log.info(f"Epoch {epoch + 1}: Train Loss={train_losses[-1]:.4f}, RMSE={rmse_train:.4f}")
            log.info(f"              Val Loss={val_losses[-1]:.4f}, RMSE={rmse_val:.4f}")
            log.info(f"              Accuracies - Exact: {metrics['exact_match']:.3f}, "
                    f"Partial: {metrics['partial_match']:.3f}, Vector: {metrics['vector_match']:.3f}")
            log.info(f"              Cosine Sim: {metrics['cosine_similarity']:.3f}, Top-5: {metrics['top5_match']:.3f}")

        elif (epoch + 1) % 10 == 0 or epoch == 0:
            metrics = val_accuracies[-1]
            log.info(f"Epoch {epoch + 1}: Train Loss={train_losses[-1]:.4f}, RMSE={rmse_train:.4f} | "
                    f"Val Loss={val_losses[-1]:.4f}, RMSE={rmse_val:.4f}")
            log.info(f"              Exact Acc: {metrics['exact_match']:.3f}, "
                    f"Partial Acc: {metrics['partial_match']:.3f}")

    log.info(f"Model saved at best epoch with val loss: {best_val_loss:.4f}")

    visualize_training_progress(train_losses, train_rmses, save_dir,
                                val_losses=val_losses, val_rmses=val_rmses)

    train_rmse = evaluate_model(model, train_loader, tokenizer, save_dir, device, mode="training", use_checkpoint=True)
    val_rmse = evaluate_model(model, val_loader, tokenizer, save_dir, device, mode="validation", use_checkpoint=True)
    test_rmse = evaluate_model(model, test_loader, tokenizer, save_dir, device, mode="testing", use_checkpoint=True)

    import csv
    metrics_path = Path(os.getcwd()) / "all_metrics.csv"
    log.info(f"Save to: {metrics_path}")

    fieldnames = ["d_model", "nhead", "num_layers", "dropout", "dim_feedforward", "num_parameters", 
                  "train_acc", "val_acc", "test_acc"]

    with open(metrics_path, "a", newline="") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writerow({
            "d_model": cfg.model.d_model,
            "nhead": cfg.model.nhead,
            "num_layers": cfg.model.num_layers,
            "dropout": cfg.model.dropout,
            "dim_feedforward": cfg.model.dim_feedforward,
            "num_parameters": count_parameters(model),
            "train_acc": f"{train_rmse:.4f}",
            "val_acc": f"{val_rmse:.4f}",
            "test_acc": f"{test_rmse:.4f}",
        })

if __name__ == "__main__":
    main()
