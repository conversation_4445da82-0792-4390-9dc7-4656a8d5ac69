model:
  _target_: src.transformer_model.TransformerDictionarySequencePredictor
  d_model: 128
  nhead: 4
  num_layers: 3
  dim_feedforward: 512
  dropout: 0.1
  seq_len: 5
paths:
  dict_path: data/unique_sorted_lines_dictionary.txt
  train_path: data/random_cont_test_8.0_train.txt
  test_path: data/random_cont_test_8.0_test.txt
  save_dir: log
training:
  seed: 42
  val_ratio: 0.2
  batch_size: 4096
  num_epochs: 20
  learning_rate: 0.001
  loss_multiplier: 1.0
