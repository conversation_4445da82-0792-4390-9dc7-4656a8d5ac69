[2025-07-10 04:11:16,211][__main__][INFO] - Configuration:
model:
  _target_: src.transformer_model.TransformerDictionarySequencePredictor
  d_model: 128
  nhead: 4
  num_layers: 3
  dim_feedforward: 512
  dropout: 0.1
  seq_len: 5
paths:
  dict_path: data/unique_sorted_lines_dictionary.txt
  train_path: data/random_cont_test_8.0_train.txt
  test_path: data/random_cont_test_8.0_test.txt
  save_dir: log
training:
  seed: 42
  val_ratio: 0.2
  batch_size: 4096
  num_epochs: 20
  learning_rate: 0.001
  loss_multiplier: 1.0

[2025-07-10 04:11:16,241][__main__][INFO] - Using device: cuda
[2025-07-10 04:11:16,242][__main__][INFO] - Saving outputs to: C:\Zikui\Personal\17. 24Fall\Syy\SYY-07-10\my_experiments\2025-07-10_04-11-16\log
[2025-07-10 04:11:16,251][src.dataset][INFO] - Dictionary size: 261, vector dim: 8
[2025-07-10 04:11:20,183][__main__][INFO] - Model has 662,533 trainable parameters.
[2025-07-10 04:11:23,308][__main__][INFO] - Epoch 1 is best so far!
[2025-07-10 04:11:23,308][__main__][INFO] - Epoch 1: Train Loss=4.0042, RMSE=1.8829 | Val Loss=3.5609, RMSE=1.8870
[2025-07-10 04:11:23,308][__main__][INFO] - --------------------------
[2025-07-10 04:11:23,802][src.eval_utils][INFO] - Final training RMSE: 2.2645
[2025-07-10 04:11:24,287][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:24,287][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:25,548][src.eval_utils][INFO] - Final validation RMSE: 2.2635
[2025-07-10 04:11:25,661][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:25,661][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:28,094][__main__][INFO] - Epoch 2 is best so far!
[2025-07-10 04:11:28,095][__main__][INFO] - Epoch 2: Train Loss=3.4627, RMSE=1.8352 | Val Loss=3.3928, RMSE=1.8419
[2025-07-10 04:11:28,095][__main__][INFO] - --------------------------
[2025-07-10 04:11:28,582][src.eval_utils][INFO] - Final training RMSE: 2.1833
[2025-07-10 04:11:29,049][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:29,049][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:30,252][src.eval_utils][INFO] - Final validation RMSE: 2.2022
[2025-07-10 04:11:30,380][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:30,380][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:32,882][__main__][INFO] - Epoch 3 is best so far!
[2025-07-10 04:11:32,883][__main__][INFO] - Epoch 3: Train Loss=3.3544, RMSE=1.8191 | Val Loss=3.3476, RMSE=1.8296
[2025-07-10 04:11:32,883][__main__][INFO] - --------------------------
[2025-07-10 04:11:33,393][src.eval_utils][INFO] - Final training RMSE: 2.1557
[2025-07-10 04:11:33,878][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:33,879][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:35,095][src.eval_utils][INFO] - Final validation RMSE: 2.1727
[2025-07-10 04:11:35,209][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:35,209][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:37,721][__main__][INFO] - Epoch 4 is best so far!
[2025-07-10 04:11:37,722][__main__][INFO] - Epoch 4: Train Loss=3.2714, RMSE=1.7865 | Val Loss=3.2658, RMSE=1.8072
[2025-07-10 04:11:37,722][__main__][INFO] - --------------------------
[2025-07-10 04:11:38,197][src.eval_utils][INFO] - Final training RMSE: 2.2305
[2025-07-10 04:11:38,681][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:38,682][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:39,852][src.eval_utils][INFO] - Final validation RMSE: 2.2617
[2025-07-10 04:11:39,957][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:39,958][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:42,553][__main__][INFO] - Epoch 5 is best so far!
[2025-07-10 04:11:42,553][__main__][INFO] - Epoch 5: Train Loss=3.1870, RMSE=1.7740 | Val Loss=3.2423, RMSE=1.8007
[2025-07-10 04:11:42,553][__main__][INFO] - --------------------------
[2025-07-10 04:11:43,241][src.eval_utils][INFO] - Final training RMSE: 2.2090
[2025-07-10 04:11:43,720][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:43,720][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:44,650][src.eval_utils][INFO] - Final validation RMSE: 2.2425
[2025-07-10 04:11:44,772][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:44,772][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:47,322][__main__][INFO] - Epoch 6 is best so far!
[2025-07-10 04:11:47,322][__main__][INFO] - Epoch 6: Train Loss=3.1567, RMSE=1.7672 | Val Loss=3.2376, RMSE=1.7993
[2025-07-10 04:11:47,323][__main__][INFO] - --------------------------
[2025-07-10 04:11:48,056][src.eval_utils][INFO] - Final training RMSE: 2.1679
[2025-07-10 04:11:48,547][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:48,547][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:49,511][src.eval_utils][INFO] - Final validation RMSE: 2.2092
[2025-07-10 04:11:49,623][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:49,623][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:52,107][__main__][INFO] - Epoch 7 is best so far!
[2025-07-10 04:11:52,108][__main__][INFO] - Epoch 7: Train Loss=3.1255, RMSE=1.7574 | Val Loss=3.2354, RMSE=1.7987
[2025-07-10 04:11:52,108][__main__][INFO] - --------------------------
[2025-07-10 04:11:52,814][src.eval_utils][INFO] - Final training RMSE: 2.1667
[2025-07-10 04:11:53,292][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:53,292][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:54,227][src.eval_utils][INFO] - Final validation RMSE: 2.2209
[2025-07-10 04:11:54,340][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:54,341][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:11:58,603][__main__][INFO] - Epoch 9 is best so far!
[2025-07-10 04:11:58,604][__main__][INFO] - Epoch 9: Train Loss=3.0858, RMSE=1.7416 | Val Loss=3.2351, RMSE=1.7986
[2025-07-10 04:11:58,604][__main__][INFO] - --------------------------
[2025-07-10 04:11:59,085][src.eval_utils][INFO] - Final training RMSE: 2.1283
[2025-07-10 04:11:59,582][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:11:59,582][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:12:00,568][src.eval_utils][INFO] - Final validation RMSE: 2.2094
[2025-07-10 04:12:00,677][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:12:00,677][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:12:03,520][__main__][INFO] - Epoch 10: Train Loss=3.0511, RMSE=1.7274 | Val Loss=3.2624, RMSE=1.8062
[2025-07-10 04:12:19,248][__main__][INFO] - Epoch 20: Train Loss=2.4895, RMSE=1.5032 | Val Loss=3.5802, RMSE=1.8921
[2025-07-10 04:12:19,249][__main__][INFO] - Model saved at best epoch with val loss: 3.2351
[2025-07-10 04:12:19,970][src.eval_utils][INFO] - Final training RMSE: 2.1283
[2025-07-10 04:12:20,467][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:12:20,467][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:12:21,422][src.eval_utils][INFO] - Final validation RMSE: 2.2094
[2025-07-10 04:12:21,551][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:12:21,551][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:12:22,691][src.eval_utils][INFO] - Final testing RMSE: 2.2149
[2025-07-10 04:12:22,751][src.eval_utils][INFO] - Labels with 100% accuracy: 0
[2025-07-10 04:12:22,752][src.eval_utils][INFO] - Perfect labels: []
[2025-07-10 04:12:23,555][__main__][INFO] - Save to: C:\Zikui\Personal\17. 24Fall\Syy\SYY-07-10\all_metrics.csv
