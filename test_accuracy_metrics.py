#!/usr/bin/env python3
"""
Test script to demonstrate the accuracy metrics
"""

import torch
import numpy as np

def compute_accuracy_metrics(pred_vecs, true_vecs, logits, y_batch, threshold=0.5):
    """
    Compute various accuracy metrics for vector predictions.
    """
    # 1. Exact Token Match Accuracy (traditional classification accuracy)
    pred_tokens = logits.argmax(dim=-1)
    exact_match_acc = (pred_tokens == y_batch).float().mean().item()
    
    # 2. Element-wise Partial Match Accuracy (% of elements within threshold)
    element_errors = torch.abs(pred_vecs - true_vecs)
    element_matches = (element_errors < threshold).float()
    partial_match_acc = element_matches.mean().item()
    
    # 3. Vector-level Accuracy (% of vectors where ALL elements are within threshold)
    vector_matches = (element_matches.mean(dim=-1) == 1.0).float()
    vector_acc = vector_matches.mean().item()
    
    # 4. Cosine Similarity Accuracy (average cosine similarity)
    cos_sim = torch.nn.functional.cosine_similarity(pred_vecs, true_vecs, dim=-1)
    cos_sim_acc = cos_sim.mean().item()
    
    # 5. Top-K Token Accuracy (is ground truth in top-k predictions?)
    top5_preds = logits.topk(5, dim=-1)[1]  # Get top-5 token indices
    top5_acc = (top5_preds == y_batch.unsqueeze(-1)).any(dim=-1).float().mean().item()
    
    return {
        'exact_match': exact_match_acc,
        'partial_match': partial_match_acc,
        'vector_match': vector_acc,
        'cosine_similarity': cos_sim_acc,
        'top5_match': top5_acc
    }

def test_accuracy_metrics():
    print("Testing Accuracy Metrics")
    print("=" * 50)
    
    # Simulate a small vocabulary with vector representations
    vocab_size = 6
    vector_dim = 3
    batch_size = 4
    
    # Create mock class_values (like your tokenizer.class_values)
    class_values = torch.tensor([
        [1.0, 0.0, 0.0],  # Token 0
        [0.0, 1.0, 0.0],  # Token 1  
        [0.0, 0.0, 1.0],  # Token 2
        [1.0, 1.0, 0.0],  # Token 3
        [1.0, 0.0, 1.0],  # Token 4
        [0.0, 1.0, 1.0],  # Token 5
    ])
    
    print(f"Vocabulary size: {vocab_size}")
    print(f"Vector dimension: {vector_dim}")
    print(f"Class values:\n{class_values}")
    print()
    
    # Test Case 1: Perfect predictions
    print("Test 1: Perfect Predictions")
    y_batch = torch.tensor([0, 1, 2, 3])  # Ground truth tokens
    logits_perfect = torch.zeros(batch_size, vocab_size)
    logits_perfect[0, 0] = 10.0  # Strongly predict token 0
    logits_perfect[1, 1] = 10.0  # Strongly predict token 1
    logits_perfect[2, 2] = 10.0  # Strongly predict token 2
    logits_perfect[3, 3] = 10.0  # Strongly predict token 3
    
    probs_perfect = torch.softmax(logits_perfect, dim=-1)
    pred_vecs_perfect = probs_perfect @ class_values
    true_vecs = class_values[y_batch]
    
    metrics_perfect = compute_accuracy_metrics(pred_vecs_perfect, true_vecs, logits_perfect, y_batch)
    
    print(f"Ground truth tokens: {y_batch}")
    print(f"Predicted tokens: {logits_perfect.argmax(dim=-1)}")
    print(f"Metrics: {metrics_perfect}")
    print()
    
    # Test Case 2: Partial predictions (some close, some wrong)
    print("Test 2: Partial Predictions")
    logits_partial = torch.tensor([
        [8.0, 2.0, 0.0, 0.0, 0.0, 0.0],  # Should predict 0, does predict 0
        [2.0, 6.0, 2.0, 0.0, 0.0, 0.0],  # Should predict 1, does predict 1  
        [0.0, 0.0, 3.0, 7.0, 0.0, 0.0],  # Should predict 2, predicts 3 (wrong)
        [1.0, 1.0, 1.0, 5.0, 4.0, 1.0],  # Should predict 3, does predict 3
    ])
    
    probs_partial = torch.softmax(logits_partial, dim=-1)
    pred_vecs_partial = probs_partial @ class_values
    
    metrics_partial = compute_accuracy_metrics(pred_vecs_partial, true_vecs, logits_partial, y_batch)
    
    print(f"Ground truth tokens: {y_batch}")
    print(f"Predicted tokens: {logits_partial.argmax(dim=-1)}")
    print(f"Ground truth vectors:\n{true_vecs}")
    print(f"Predicted vectors:\n{pred_vecs_partial}")
    print(f"Metrics: {metrics_partial}")
    print()
    
    # Test Case 3: Random predictions
    print("Test 3: Random Predictions")
    logits_random = torch.randn(batch_size, vocab_size)
    probs_random = torch.softmax(logits_random, dim=-1)
    pred_vecs_random = probs_random @ class_values
    
    metrics_random = compute_accuracy_metrics(pred_vecs_random, true_vecs, logits_random, y_batch)
    
    print(f"Ground truth tokens: {y_batch}")
    print(f"Predicted tokens: {logits_random.argmax(dim=-1)}")
    print(f"Metrics: {metrics_random}")
    print()
    
    # Explain the metrics
    print("Metric Explanations:")
    print("- exact_match: Traditional classification accuracy (predicted token == true token)")
    print("- partial_match: Average fraction of vector elements within threshold")
    print("- vector_match: Fraction of vectors where ALL elements are within threshold")
    print("- cosine_similarity: Average cosine similarity between predicted and true vectors")
    print("- top5_match: Fraction where true token is in top-5 predictions")

if __name__ == "__main__":
    test_accuracy_metrics()
